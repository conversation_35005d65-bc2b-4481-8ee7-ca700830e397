import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

// This endpoint can be called by external cron services like Vercel Cron or GitHub Actions
export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional security measure)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('🧹 Cron: Unauthorized cleanup attempt')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('🧹 Cron: Starting scheduled abandoned orders cleanup...')

    // Get orders that are pending for more than 24 hours
    const twentyFourHoursAgo = new Date()
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24)

    // Find abandoned orders (pending status, older than 24 hours)
    const { data: abandonedOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        payment_intent_id,
        status,
        payment_status,
        created_at,
        email,
        total_amount
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .lt('created_at', twentyFourHoursAgo.toISOString())

    if (fetchError) {
      console.error('🧹 Cron: Error fetching abandoned orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch abandoned orders' },
        { status: 500 }
      )
    }

    if (!abandonedOrders || abandonedOrders.length === 0) {
      console.log('🧹 Cron: No abandoned orders found')
      return NextResponse.json({
        success: true,
        message: 'No abandoned orders found',
        cleanedCount: 0,
        timestamp: new Date().toISOString()
      })
    }

    console.log(`🧹 Cron: Found ${abandonedOrders.length} abandoned orders to clean up`)

    // Cancel abandoned orders
    const { data: cancelledOrders, error: cancelError } = await supabaseAdmin
      .from('orders')
      .update({
        status: 'cancelled',
        payment_status: 'failed',
        notes: 'Automatically cancelled - abandoned checkout after 24 hours'
      })
      .in('id', abandonedOrders.map(order => order.id))
      .select()

    if (cancelError) {
      console.error('🧹 Cron: Error cancelling abandoned orders:', cancelError)
      return NextResponse.json(
        { error: 'Failed to cancel abandoned orders' },
        { status: 500 }
      )
    }

    // Cancel Stripe Payment Intents for abandoned orders
    const { default: Stripe } = await import('stripe')
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)
    let stripeCancelledCount = 0
    const stripeErrors: string[] = []

    for (const order of abandonedOrders) {
      if (order.payment_intent_id) {
        try {
          const paymentIntent = await stripe.paymentIntents.retrieve(order.payment_intent_id)
          
          // Only cancel if the payment intent is still in a cancellable state
          if (['requires_payment_method', 'requires_confirmation', 'requires_action'].includes(paymentIntent.status)) {
            await stripe.paymentIntents.cancel(order.payment_intent_id)
            stripeCancelledCount++
            console.log(`🧹 Cron: Cancelled Stripe Payment Intent ${order.payment_intent_id}`)
          } else {
            console.log(`🧹 Cron: Payment Intent ${order.payment_intent_id} is in status ${paymentIntent.status}, skipping cancellation`)
          }
        } catch (stripeError: unknown) {
          const errorMessage = `Failed to cancel Payment Intent ${order.payment_intent_id}: ${stripeError instanceof Error ? stripeError.message : 'Unknown error'}`
          console.error(`🧹 Cron: ${errorMessage}`)
          stripeErrors.push(errorMessage)
          // Continue with other orders even if one fails
        }
      }
    }

    const result = {
      success: true,
      message: `Cleaned up ${cancelledOrders?.length || 0} abandoned orders`,
      cleanedCount: cancelledOrders?.length || 0,
      stripeCancelledCount,
      stripeErrors: stripeErrors.length > 0 ? stripeErrors : undefined,
      timestamp: new Date().toISOString(),
      cleanedOrders: abandonedOrders.map(order => ({
        id: order.id,
        email: order.email,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      }))
    }

    console.log(`🧹 Cron: Cleanup completed successfully. Cancelled ${cancelledOrders?.length || 0} orders and ${stripeCancelledCount} Stripe Payment Intents`)

    return NextResponse.json(result)

  } catch (error) {
    console.error('🧹 Cron: Error in scheduled cleanup:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// POST method for manual triggering (same logic as GET)
export async function POST(request: NextRequest) {
  return GET(request)
}
