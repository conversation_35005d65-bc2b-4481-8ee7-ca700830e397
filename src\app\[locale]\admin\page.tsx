'use client'

import { useEffect, useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import {
  ShoppingCart,
  Users,
  Package,
  TrendingUp,
  DollarSign,
  Calendar,
  Eye,
  AlertCircle,
  Percent,
  Truck,
  Trophy,
  Mail,
  Trash2
} from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function AdminDashboard() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [currentMonthRevenue, setCurrentMonthRevenue] = useState(0)
  const [currentMonthOrderCount, setCurrentMonthOrderCount] = useState(0)
  const [avgOrderValue, setAvgOrderValue] = useState(0)
  const [totalCustomers, setTotalCustomers] = useState(0)
  const [totalProducts, setTotalProducts] = useState(0)
  const [recentOrders, setRecentOrders] = useState<Array<{
    id: string;
    total_amount: number;
    status: string;
    created_at: string;
    shipping_address: unknown;
  }>>([])
  const [currentMonthOrders, setCurrentMonthOrders] = useState<Array<{
    total_amount: number;
    status: string;
    created_at: string;
  }>>([])
  const [recentOrdersFilter, setRecentOrdersFilter] = useState<'all' | 'completed'>('all')

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return // Prevent multiple checks

        console.log('Checking authentication...')
        // Check if user is admin
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        // Get user profile to check admin status
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          console.log('Profile error details:', profileError.message, profileError.code)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading dashboard data')
        setAuthChecked(true)

        // Load dashboard data inline to avoid dependency issues
        try {
          console.log('Loading dashboard data...')

          // Current month orders
          const startOfMonth = new Date()
          startOfMonth.setDate(1)
          startOfMonth.setHours(0, 0, 0, 0)

          const { data: currentMonthOrdersData, error: currentMonthError } = await supabase
            .from('orders')
            .select('total_amount, status, created_at')
            .gte('created_at', startOfMonth.toISOString())

          if (currentMonthError) {
            console.error('Error loading current month orders:', currentMonthError)
          }

          // Total customers
          const { count: totalCustomersCount, error: customersError } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true })

          if (customersError) {
            console.error('Error loading customers:', customersError)
          }

          // Total products
          const { count: totalProductsCount, error: productsError } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true })

          if (productsError) {
            console.error('Error loading products:', productsError)
          }

          // Recent orders
          const { data: recentOrdersData, error: recentOrdersError } = await supabase
            .from('orders')
            .select(`
              id,
              total_amount,
              status,
              created_at,
              shipping_address
            `)
            .order('created_at', { ascending: false })
            .limit(5)

          if (recentOrdersError) {
            console.error('Error loading recent orders:', recentOrdersError)
          }

          // Calculate statistics
          const revenue = currentMonthOrdersData?.reduce((sum: number, order: { total_amount: number }) => sum + order.total_amount, 0) || 0
          const orderCount = currentMonthOrdersData?.length || 0
          const avgOrder = orderCount > 0 ? revenue / orderCount : 0

          console.log('Dashboard data loaded successfully')
          setCurrentMonthRevenue(revenue)
          setCurrentMonthOrderCount(orderCount)
          setAvgOrderValue(avgOrder)
          setTotalCustomers(totalCustomersCount || 0)
          setTotalProducts(totalProductsCount || 0)
          setRecentOrders(recentOrdersData || [])
          setCurrentMonthOrders(currentMonthOrdersData || [])
          setLoading(false)
        } catch (dashboardError) {
          console.error('Error loading dashboard data:', dashboardError)
          setLoading(false)
        }
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-muted-foreground">
          {t('dashboardSubtitle')}
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-4 gap-4 mb-8">
        <Button asChild>
          <Link href={`/${locale}/admin/products`}>
            <Package className="mr-2 h-4 w-4" />
            {t('productsNav')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/orders`}>
            <ShoppingCart className="mr-2 h-4 w-4" />
            {t('orders')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/customers`}>
            <Users className="mr-2 h-4 w-4" />
            {t('customers')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/analytics`}>
            <TrendingUp className="mr-2 h-4 w-4" />
            {t('analytics')}
          </Link>
        </Button>
      </div>

      {/* Additional Admin Tools */}
      <div className="grid md:grid-cols-4 gap-4 mb-8">
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/coupons`}>
            <Percent className="mr-2 h-4 w-4" />
            {t('coupons')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/bundles`}>
            <Package className="mr-2 h-4 w-4" />
            {t('bundles')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/shipping`}>
            <Truck className="mr-2 h-4 w-4" />
            {t('shipping')}
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/gamification`}>
            <Trophy className="mr-2 h-4 w-4" />
            {t('gamification')}
          </Link>
        </Button>
      </div>

      {/* System Management Tools */}
      <div className="grid md:grid-cols-4 gap-4 mb-8">
        <Button variant="outline" asChild>
          <Link href={`/${locale}/admin/cleanup`}>
            <Trash2 className="mr-2 h-4 w-4" />
            Pulizia Ordini
          </Link>
        </Button>
      </div>

      {/* Development Tools */}
      {process.env.NODE_ENV === 'development' && (
        <div className="grid md:grid-cols-4 gap-4 mb-8">
          <Button variant="outline" asChild>
            <Link href={`/${locale}/admin/email-test`}>
              <Mail className="mr-2 h-4 w-4" />
              {t('emailTest')}
            </Link>
          </Button>
        </div>
      )}

      {/* KPI Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('monthlyRevenue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentMonthRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              {t('currentMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('monthlyOrders')}</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentMonthOrderCount}</div>
            <p className="text-xs text-muted-foreground">
              {t('currentMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('avgOrderValue')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(avgOrderValue)}</div>
            <p className="text-xs text-muted-foreground">
              {t('avgThisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('totalCustomers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {t('registeredUsers')}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('recentOrdersTitle')}
            </CardTitle>
            <div className="flex gap-2 mt-4">
              <button
                onClick={() => setRecentOrdersFilter('all')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  recentOrdersFilter === 'all'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
              >
                Tutti
              </button>
              <button
                onClick={() => setRecentOrdersFilter('completed')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  recentOrdersFilter === 'completed'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
              >
                Completati
              </button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders
                ?.filter((order) => {
                  if (recentOrdersFilter === 'completed') {
                    return ['confirmed', 'processing', 'shipped', 'delivered'].includes(order.status)
                  }
                  return true // 'all' shows everything
                })
                ?.map((order: {
                  id: string;
                  total_amount: number;
                  status: string;
                  created_at: string;
                  shipping_address: unknown
                }) => {
                  const shippingAddress = order.shipping_address as {
                    firstName?: string
                    lastName?: string
                  }
                  return (
                    <div
                      key={order.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                      onClick={() => router.push(`/${locale}/admin/orders/${order.id}`)}
                    >
                      <div>
                        <div className="font-medium">
                          {shippingAddress?.firstName} {shippingAddress?.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(order.created_at).toLocaleDateString(locale === 'de' ? 'de-CH' : locale === 'fr' ? 'fr-CH' : 'it-CH')}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{formatCurrency(order.total_amount)}</div>
                        <Badge variant="default">
                          {order.status}
                        </Badge>
                      </div>
                    </div>
                  )
                })}
              {(!recentOrders || recentOrders.length === 0) && (
                <p className="text-muted-foreground text-center py-4">
                  {t('noOrdersFound')}
                </p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/${locale}/admin/orders`}>
                  <Eye className="mr-2 h-4 w-4" />
                  {t('viewAllOrders')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              {t('systemStatusTitle')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 border border-green-200 bg-green-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="font-medium text-green-800">{t('systemStatusTitle')}</span>
                </div>
                <div className="text-sm text-green-700">
                  {t('allSystemsNormal')}
                </div>
              </div>

              <div className="p-3 border rounded-lg">
                <div className="font-medium mb-2">{t('quickOverview')}</div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">{t('productsNav')}</div>
                    <div className="font-semibold">{totalProducts}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">{t('today')}</div>
                    <div className="font-semibold">
                      {currentMonthOrders?.filter((order: { created_at: string }) =>
                        new Date(order.created_at).toDateString() === new Date().toDateString()
                      ).length || 0} {t('orders')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
